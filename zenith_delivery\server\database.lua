-- Database initialization and management for Zenith Delivery System

local function CreateTables()
    -- Player delivery data table
    MySQL.query([[
        CREATE TABLE IF NOT EXISTS zenith_delivery_players (
            citizenid VARCHAR(50) PRIMARY KEY,
            level INT DEFAULT 1,
            xp INT DEFAULT 0,
            total_deliveries INT DEFAULT 0,
            total_earnings INT DEFAULT 0,
            total_distance FLOAT DEFAULT 0.0,
            reputation INT DEFAULT 0,
            last_job_time TIMESTAMP NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )
    ]])
    
    -- Job history table
    MySQL.query([[
        CREATE TABLE IF NOT EXISTS zenith_delivery_jobs (
            id INT AUTO_INCREMENT PRIMARY KEY,
            citizenid VARCHAR(50),
            job_type VARCHAR(50),
            job_data JSON,
            status ENUM('active', 'completed', 'failed', 'abandoned') DEFAULT 'active',
            payment INT DEFAULT 0,
            xp_earned INT DEFAULT 0,
            start_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            completion_time TIMESTAMP NULL,
            failure_reason VARCHAR(255) NULL,
            distance_traveled FLOAT DEFAULT 0.0,
            stops_completed INT DEFAULT 0,
            total_stops INT DEFAULT 0,
            vehicle_used VARCHAR(50),
            INDEX idx_citizenid (citizenid),
            INDEX idx_status (status),
            INDEX idx_start_time (start_time)
        )
    ]])
    
    -- Vehicle ownership table (integrates with QBOX garage)
    MySQL.query([[
        CREATE TABLE IF NOT EXISTS zenith_delivery_vehicles (
            id INT AUTO_INCREMENT PRIMARY KEY,
            citizenid VARCHAR(50),
            vehicle_model VARCHAR(50),
            purchase_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            total_distance FLOAT DEFAULT 0.0,
            total_jobs INT DEFAULT 0,
            condition_rating FLOAT DEFAULT 100.0,
            INDEX idx_citizenid (citizenid),
            INDEX idx_vehicle_model (vehicle_model)
        )
    ]])
    
    -- Achievements/unlocks table
    MySQL.query([[
        CREATE TABLE IF NOT EXISTS zenith_delivery_achievements (
            id INT AUTO_INCREMENT PRIMARY KEY,
            citizenid VARCHAR(50),
            achievement_id VARCHAR(100),
            unlocked_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_citizenid (citizenid),
            INDEX idx_achievement (achievement_id),
            UNIQUE KEY unique_achievement (citizenid, achievement_id)
        )
    ]])
    
    print("^2[Zenith Delivery]^7 Database tables created successfully")
end

-- Get player delivery data
function GetPlayerData(citizenid)
    local result = MySQL.query.await('SELECT * FROM zenith_delivery_players WHERE citizenid = ?', {citizenid})
    
    if result and result[1] then
        return result[1]
    else
        -- Create new player record
        MySQL.insert('INSERT INTO zenith_delivery_players (citizenid) VALUES (?)', {citizenid})
        return {
            citizenid = citizenid,
            level = 1,
            xp = 0,
            total_deliveries = 0,
            total_earnings = 0,
            total_distance = 0.0,
            reputation = 0,
            last_job_time = nil
        }
    end
end

-- Update player data
function UpdatePlayerData(citizenid, data)
    local setClause = {}
    local values = {}
    
    for key, value in pairs(data) do
        if key ~= 'citizenid' then
            table.insert(setClause, key .. ' = ?')
            table.insert(values, value)
        end
    end
    
    table.insert(values, citizenid)
    
    local query = 'UPDATE zenith_delivery_players SET ' .. table.concat(setClause, ', ') .. ' WHERE citizenid = ?'
    MySQL.update(query, values)
end

-- Add XP and handle level ups
function AddPlayerXP(citizenid, xp)
    local playerData = GetPlayerData(citizenid)
    local newXP = playerData.xp + xp
    local currentLevel = playerData.level
    local newLevel = currentLevel
    
    -- Calculate new level
    local xpRequired = Config.Leveling.baseXP
    local level = 1
    
    while newXP >= xpRequired and level < Config.Leveling.maxLevel do
        newXP = newXP - xpRequired
        level = level + 1
        xpRequired = math.floor(xpRequired * Config.Leveling.xpMultiplier)
    end
    
    newLevel = level
    
    -- Update player data
    UpdatePlayerData(citizenid, {
        level = newLevel,
        xp = newXP
    })
    
    -- Check for level up rewards
    if newLevel > currentLevel then
        local Player = exports.qbx_core:GetPlayer(citizenid)
        if Player then
            TriggerClientEvent('zenith_delivery:levelUp', Player.PlayerData.source, newLevel, currentLevel)
            
            -- Give level up rewards
            if Config.Leveling.levelRewards[newLevel] then
                local reward = Config.Leveling.levelRewards[newLevel]
                if reward.money then
                    Player.Functions.AddMoney('bank', reward.money)
                end
                
                TriggerClientEvent('ox_lib:notify', Player.PlayerData.source, {
                    title = 'Zenith Logistics',
                    description = reward.message or 'Level up reward received!',
                    type = 'success',
                    duration = 7000
                })
            end
        end
    end
    
    return newLevel > currentLevel, newLevel
end

-- Save job to history
function SaveJobHistory(citizenid, jobData)
    MySQL.insert([[
        INSERT INTO zenith_delivery_jobs 
        (citizenid, job_type, job_data, status, payment, xp_earned, completion_time, 
         distance_traveled, stops_completed, total_stops, vehicle_used) 
        VALUES (?, ?, ?, ?, ?, ?, NOW(), ?, ?, ?, ?)
    ]], {
        citizenid,
        jobData.type,
        json.encode(jobData),
        jobData.status,
        jobData.payment or 0,
        jobData.xp_earned or 0,
        jobData.distance_traveled or 0.0,
        jobData.stops_completed or 0,
        jobData.total_stops or 0,
        jobData.vehicle_used
    })
end

-- Get job history for player
function GetJobHistory(citizenid, limit)
    limit = limit or 50
    local result = MySQL.query.await([[
        SELECT * FROM zenith_delivery_jobs 
        WHERE citizenid = ? 
        ORDER BY start_time DESC 
        LIMIT ?
    ]], {citizenid, limit})
    
    return result or {}
end

-- Get player statistics
function GetPlayerStats(citizenid)
    local result = MySQL.query.await([[
        SELECT 
            COUNT(*) as total_jobs,
            SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed_jobs,
            SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as failed_jobs,
            AVG(CASE WHEN status = 'completed' THEN payment ELSE NULL END) as avg_payment,
            SUM(distance_traveled) as total_distance,
            MAX(payment) as highest_payment
        FROM zenith_delivery_jobs 
        WHERE citizenid = ?
    ]], {citizenid})
    
    if result and result[1] then
        return result[1]
    else
        return {
            total_jobs = 0,
            completed_jobs = 0,
            failed_jobs = 0,
            avg_payment = 0,
            total_distance = 0.0,
            highest_payment = 0
        }
    end
end

-- Vehicle ownership functions
function PurchaseVehicle(citizenid, vehicleModel)
    MySQL.insert([[
        INSERT INTO zenith_delivery_vehicles (citizenid, vehicle_model) 
        VALUES (?, ?)
    ]], {citizenid, vehicleModel})
end

function GetOwnedVehicles(citizenid)
    local result = MySQL.query.await([[
        SELECT * FROM zenith_delivery_vehicles 
        WHERE citizenid = ?
    ]], {citizenid})
    
    return result or {}
end

-- Initialize database on resource start
CreateEvent('onResourceStart', function(resourceName)
    if GetCurrentResourceName() == resourceName then
        CreateTables()
    end
end)

-- Export functions for use in other server files
exports('GetPlayerData', GetPlayerData)
exports('UpdatePlayerData', UpdatePlayerData)
exports('AddPlayerXP', AddPlayerXP)
exports('SaveJobHistory', SaveJobHistory)
exports('GetJobHistory', GetJobHistory)
exports('GetPlayerStats', GetPlayerStats)
exports('PurchaseVehicle', PurchaseVehicle)
exports('GetOwnedVehicles', GetOwnedVehicles)
