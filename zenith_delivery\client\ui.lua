-- UI management for Zenith Delivery System

local QBCore = exports['qbx_core']:GetCoreObject()

-- NUI Message handlers
RegisterNUICallback('getVehicleStats', function(data, cb)
    if not data.vehicleModel then
        cb(nil)
        return
    end
    
    local vehicleData = Config.Vehicles[data.vehicleModel]
    if vehicleData then
        cb(vehicleData)
    else
        cb(nil)
    end
end)

RegisterNUICallback('getJobDetails', function(data, cb)
    if not data.jobId then
        cb(nil)
        return
    end
    
    -- This would typically fetch from server, but for now return from config
    local jobType = data.jobType
    local jobConfig = Config.JobTypes[jobType]
    
    if jobConfig then
        cb(jobConfig)
    else
        cb(nil)
    end
end)

RegisterNUICallback('spawnVehicle', function(data, cb)
    if not data.vehicleModel then
        cb('error')
        return
    end
    
    local vehicleData = Config.Vehicles[data.vehicleModel]
    if not vehicleData then
        cb('error')
        return
    end
    
    -- Check if player owns this vehicle
    QBCore.Functions.TriggerCallback('zenith_delivery:checkVehicleOwnership', function(owns)
        if not owns then
            QBCore.Functions.Notify('You do not own this vehicle.', 'error')
            cb('error')
            return
        end
        
        SpawnDeliveryVehicle(data.vehicleModel)
        cb('ok')
    end, data.vehicleModel)
end)

RegisterNUICallback('getPlayerStats', function(data, cb)
    QBCore.Functions.TriggerCallback('zenith_delivery:getPlayerStats', function(stats)
        cb(stats or {})
    end)
end)

RegisterNUICallback('getJobHistory', function(data, cb)
    QBCore.Functions.TriggerCallback('zenith_delivery:getJobHistory', function(history)
        cb(history or {})
    end, data.limit or 20)
end)

RegisterNUICallback('getLeaderboard', function(data, cb)
    QBCore.Functions.TriggerCallback('zenith_delivery:getLeaderboard', function(leaderboard)
        cb(leaderboard or {})
    end, data.category or 'level')
end)

RegisterNUICallback('getAchievements', function(data, cb)
    QBCore.Functions.TriggerCallback('zenith_delivery:getAchievements', function(achievements)
        cb(achievements or {unlocked = {}, all = {}})
    end)
end)

-- Spawn delivery vehicle
function SpawnDeliveryVehicle(vehicleModel)
    local ped = PlayerPedId()
    local coords = GetEntityCoords(ped)
    local heading = GetEntityHeading(ped)
    
    -- Find a suitable spawn location
    local spawnCoords = GetSpawnLocation(coords, heading)
    if not spawnCoords then
        QBCore.Functions.Notify('No suitable location found to spawn vehicle.', 'error')
        return
    end
    
    -- Request vehicle model
    local modelHash = GetHashKey(vehicleModel)
    RequestModel(modelHash)
    
    while not HasModelLoaded(modelHash) do
        Wait(100)
    end
    
    -- Create vehicle
    local vehicle = CreateVehicle(modelHash, spawnCoords.x, spawnCoords.y, spawnCoords.z, spawnCoords.w, true, false)
    
    if vehicle then
        -- Set vehicle properties
        SetVehicleNumberPlateText(vehicle, "ZENITH" .. math.random(10, 99))
        SetEntityAsMissionEntity(vehicle, true, true)
        SetVehicleEngineOn(vehicle, true, true, false)
        SetVehicleFuelLevel(vehicle, 100.0)
        
        -- Put player in vehicle
        TaskWarpPedIntoVehicle(ped, vehicle, -1)
        
        -- Store current vehicle reference
        currentVehicle = vehicle
        
        QBCore.Functions.Notify('Vehicle spawned successfully!', 'success')
        
        -- Add vehicle blip
        local blip = AddBlipForEntity(vehicle)
        SetBlipSprite(blip, 477)
        SetBlipColour(blip, 2)
        SetBlipScale(blip, 0.8)
        BeginTextCommandSetBlipName("STRING")
        AddTextComponentString("Delivery Vehicle")
        EndTextCommandSetBlipName(blip)
    else
        QBCore.Functions.Notify('Failed to spawn vehicle.', 'error')
    end
    
    SetModelAsNoLongerNeeded(modelHash)
end

-- Find suitable spawn location
function GetSpawnLocation(coords, heading)
    local attempts = 0
    local maxAttempts = 10
    
    while attempts < maxAttempts do
        local offsetX = math.random(-20, 20)
        local offsetY = math.random(-20, 20)
        local testCoords = vector3(coords.x + offsetX, coords.y + offsetY, coords.z)
        
        local groundZ = GetGroundZFor_3dCoord(testCoords.x, testCoords.y, testCoords.z + 10.0, false)
        local finalCoords = vector4(testCoords.x, testCoords.y, groundZ, heading)
        
        if IsSpawnPointClear(finalCoords, 3.0) then
            return finalCoords
        end
        
        attempts = attempts + 1
    end
    
    return nil
end

-- Check if spawn point is clear
function IsSpawnPointClear(coords, radius)
    local vehicles = GetGamePool('CVehicle')
    
    for _, vehicle in ipairs(vehicles) do
        local vehicleCoords = GetEntityCoords(vehicle)
        local distance = #(vector3(coords.x, coords.y, coords.z) - vehicleCoords)
        
        if distance < radius then
            return false
        end
    end
    
    return true
end

-- Vehicle damage monitoring
CreateThread(function()
    while true do
        Wait(5000) -- Check every 5 seconds
        
        if currentVehicle and DoesEntityExist(currentVehicle) then
            local engineHealth = GetVehicleEngineHealth(currentVehicle)
            local bodyHealth = GetVehicleBodyHealth(currentVehicle)
            
            -- Check for critical damage
            if engineHealth < 300 or bodyHealth < 300 then
                QBCore.Functions.Notify('Warning: Vehicle is heavily damaged!', 'error')
                
                -- If in active job, warn about potential failure
                if currentJob then
                    QBCore.Functions.Notify('Deliver cargo quickly before vehicle breaks down!', 'error')
                end
            end
            
            -- Check if vehicle is destroyed
            if engineHealth <= 0 then
                if currentJob then
                    TriggerServerEvent('zenith_delivery:failJob', 'vehicle_destroyed')
                end
                
                currentVehicle = nil
            end
        end
    end
end)

-- Vehicle exit monitoring for job validation
CreateThread(function()
    while true do
        Wait(1000)
        
        if currentJob and currentVehicle then
            local ped = PlayerPedId()
            local vehicle = GetVehiclePedIsIn(ped, false)
            
            -- Check if player left the delivery vehicle
            if vehicle ~= currentVehicle then
                -- Give player some time to get back in
                local timeLeft = 30
                local warningShown = false
                
                while timeLeft > 0 and currentJob do
                    Wait(1000)
                    timeLeft = timeLeft - 1
                    
                    -- Check if player got back in
                    vehicle = GetVehiclePedIsIn(PlayerPedId(), false)
                    if vehicle == currentVehicle then
                        break
                    end
                    
                    -- Show warning at 15 seconds
                    if timeLeft == 15 and not warningShown then
                        QBCore.Functions.Notify('Return to your delivery vehicle or the job will be abandoned!', 'error')
                        warningShown = true
                    end
                end
                
                -- If still not in vehicle, abandon job
                if timeLeft <= 0 and currentJob then
                    vehicle = GetVehiclePedIsIn(PlayerPedId(), false)
                    if vehicle ~= currentVehicle then
                        TriggerServerEvent('zenith_delivery:failJob', 'abandoned')
                    end
                end
            end
        end
    end
end)

-- Notification system for UI events
RegisterNetEvent('zenith_delivery:showNotification', function(data)
    SendNUIMessage({
        action = 'showNotification',
        data = data
    })
end)

-- Update UI with real-time data
CreateThread(function()
    while true do
        Wait(30000) -- Update every 30 seconds
        
        if isTabletOpen then
            QBCore.Functions.TriggerCallback('zenith_delivery:getPlayerData', function(data)
                if data then
                    SendNUIMessage({
                        action = 'updatePlayerData',
                        data = data
                    })
                end
            end)
        end
    end
end)

-- Handle tablet closing on certain events
RegisterNetEvent('zenith_delivery:forceCloseTablet', function()
    if isTabletOpen then
        CloseTablet()
    end
end)

-- Vehicle cleanup on resource stop
AddEventHandler('onResourceStop', function(resourceName)
    if GetCurrentResourceName() == resourceName then
        if currentVehicle and DoesEntityExist(currentVehicle) then
            DeleteEntity(currentVehicle)
        end
    end
end)
