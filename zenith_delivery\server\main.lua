-- Main server-side logic for Zenith Delivery System

local QBCore = exports['qbx_core']:GetCoreObject()
local activeJobs = {} -- Store active jobs by player source
local availableJobs = {} -- Store generated jobs for all players

-- Initialize the resource
CreateThread(function()
    print("^2[Zenith Delivery]^7 Server starting...")
    
    -- Generate initial jobs
    GenerateAvailableJobs()
    
    -- Set up job refresh timer
    SetTimeout(Config.JobGeneration.refreshInterval, function()
        RefreshAvailableJobs()
    end)
    
    print("^2[Zenith Delivery]^7 Server started successfully")
end)

-- Generate available jobs for players
function GenerateAvailableJobs()
    availableJobs = {}
    
    for i = 1, Config.JobGeneration.maxActiveJobs do
        local jobType = GetRandomJobType()
        local job = CreateJobFromType(jobType)
        table.insert(availableJobs, job)
    end
    
    -- Notify all players of new jobs
    TriggerClientEvent('zenith_delivery:updateAvailableJobs', -1, availableJobs)
end

-- Refresh available jobs periodically
function RefreshAvailableJobs()
    -- Remove some old jobs and add new ones
    local jobsToRemove = math.random(2, 4)
    for i = 1, jobsToRemove do
        if #availableJobs > 0 then
            table.remove(availableJobs, math.random(1, #availableJobs))
        end
    end
    
    -- Add new jobs
    for i = 1, jobsToRemove do
        local jobType = GetRandomJobType()
        local job = CreateJobFromType(jobType)
        table.insert(availableJobs, job)
    end
    
    TriggerClientEvent('zenith_delivery:updateAvailableJobs', -1, availableJobs)
    
    -- Schedule next refresh
    SetTimeout(Config.JobGeneration.refreshInterval, function()
        RefreshAvailableJobs()
    end)
end

-- Get random job type based on weights
function GetRandomJobType()
    local totalWeight = 0
    for _, weight in pairs(Config.JobGeneration.rarityWeights) do
        totalWeight = totalWeight + weight
    end
    
    local random = math.random(1, totalWeight)
    local currentWeight = 0
    
    for rarity, weight in pairs(Config.JobGeneration.rarityWeights) do
        currentWeight = currentWeight + weight
        if random <= currentWeight then
            -- Get random job type of this rarity
            local jobTypes = {}
            for jobId, jobData in pairs(Config.JobTypes) do
                -- Simple rarity assignment based on difficulty
                local jobRarity = "common"
                if jobData.difficulty >= 4 then
                    jobRarity = "epic"
                elseif jobData.difficulty >= 3 then
                    jobRarity = "rare"
                elseif jobData.difficulty >= 2 then
                    jobRarity = "uncommon"
                end
                
                if jobRarity == rarity then
                    table.insert(jobTypes, jobId)
                end
            end
            
            if #jobTypes > 0 then
                return jobTypes[math.random(1, #jobTypes)]
            end
        end
    end
    
    return "local_delivery" -- Fallback
end

-- Create job instance from job type
function CreateJobFromType(jobType)
    local jobConfig = Config.JobTypes[jobType]
    if not jobConfig then return nil end
    
    local job = {
        id = GenerateJobId(),
        type = jobType,
        name = jobConfig.name,
        description = jobConfig.description,
        difficulty = jobConfig.difficulty,
        requiredLevel = jobConfig.requiredLevel,
        allowedVehicles = jobConfig.allowedVehicles,
        cargoTypes = jobConfig.cargoTypes,
        estimatedTime = jobConfig.estimatedTime,
        riskLevel = jobConfig.riskLevel,
        payment = math.random(jobConfig.basePayment.min, jobConfig.basePayment.max),
        xp = math.random(jobConfig.baseXP.min, jobConfig.baseXP.max),
        stops = GenerateJobStops(jobConfig),
        specialRequirements = jobConfig.specialRequirements or false,
        requiresTrailer = jobConfig.requiresTrailer or false,
        highValue = jobConfig.highValue or false,
        timeBonus = jobConfig.timeBonus or false,
        timePenalty = jobConfig.timePenalty or false
    }
    
    -- Apply rarity multiplier
    local rarity = GetJobRarity(jobConfig.difficulty)
    local multiplier = Config.JobGeneration.rarityMultipliers[rarity] or 1.0
    job.payment = math.floor(job.payment * multiplier)
    job.xp = math.floor(job.xp * multiplier)
    job.rarity = rarity
    
    -- Apply special condition bonuses
    ApplySpecialConditions(job)
    
    return job
end

-- Generate job stops
function GenerateJobStops(jobConfig)
    local stops = {}
    local numStops = math.random(1, jobConfig.maxStops)
    
    -- Pickup location (depot)
    local pickupDepot = Config.Depots[math.random(1, #Config.Depots)]
    table.insert(stops, {
        type = "pickup",
        location = pickupDepot.name,
        coords = pickupDepot.coords,
        heading = pickupDepot.heading,
        completed = false
    })
    
    -- Delivery locations
    local usedLocations = {}
    for i = 1, numStops do
        local deliveryLocation
        repeat
            deliveryLocation = Config.DeliveryLocations[math.random(1, #Config.DeliveryLocations)]
        until not usedLocations[deliveryLocation.name]
        
        usedLocations[deliveryLocation.name] = true
        
        table.insert(stops, {
            type = "delivery",
            location = deliveryLocation.name,
            coords = deliveryLocation.coords,
            completed = false
        })
    end
    
    return stops
end

-- Get job rarity based on difficulty
function GetJobRarity(difficulty)
    if difficulty >= 5 then return "epic"
    elseif difficulty >= 4 then return "rare"
    elseif difficulty >= 3 then return "uncommon"
    else return "common" end
end

-- Apply special condition bonuses
function ApplySpecialConditions(job)
    local bonuses = {}
    
    -- Weather bonus
    local weather = GetCurrentWeather()
    if Config.SpecialConditions.weather[weather] then
        local bonus = Config.SpecialConditions.weather[weather]
        job.payment = math.floor(job.payment * bonus.multiplier)
        table.insert(bonuses, bonus.message)
    end
    
    -- Time of day bonus
    local hour = tonumber(os.date("%H"))
    if (hour >= 22 or hour <= 6) and Config.SpecialConditions.timeOfDay.night then
        local bonus = Config.SpecialConditions.timeOfDay.night
        job.payment = math.floor(job.payment * bonus.multiplier)
        table.insert(bonuses, bonus.message)
    elseif (hour >= 7 and hour <= 9) or (hour >= 17 and hour <= 19) then
        local bonus = Config.SpecialConditions.timeOfDay.rush_hour
        job.payment = math.floor(job.payment * bonus.multiplier)
        table.insert(bonuses, bonus.message)
    end
    
    job.bonuses = bonuses
end

-- Generate unique job ID
function GenerateJobId()
    return "ZD_" .. os.time() .. "_" .. math.random(1000, 9999)
end

-- Get current weather (placeholder - integrate with your weather system)
function GetCurrentWeather()
    -- This should integrate with your server's weather system
    local weathers = {"clear", "rain", "storm", "fog"}
    return weathers[math.random(1, #weathers)]
end

-- Player connected event
RegisterNetEvent('QBCore:Server:PlayerLoaded', function()
    local src = source
    local Player = QBCore.Functions.GetPlayer(src)
    if not Player then return end
    
    -- Send available jobs to player
    TriggerClientEvent('zenith_delivery:updateAvailableJobs', src, availableJobs)
    
    -- Send player data
    local playerData = exports.zenith_delivery:GetPlayerData(Player.PlayerData.citizenid)
    TriggerClientEvent('zenith_delivery:updatePlayerData', src, playerData)
end)

-- Get player data callback
QBCore.Functions.CreateCallback('zenith_delivery:getPlayerData', function(source, cb)
    local Player = QBCore.Functions.GetPlayer(source)
    if not Player then 
        cb(nil)
        return 
    end
    
    local playerData = exports.zenith_delivery:GetPlayerData(Player.PlayerData.citizenid)
    local jobHistory = exports.zenith_delivery:GetJobHistory(Player.PlayerData.citizenid, 20)
    local ownedVehicles = exports.zenith_delivery:GetOwnedVehicles(Player.PlayerData.citizenid)
    local stats = exports.zenith_delivery:GetPlayerStats(Player.PlayerData.citizenid)
    
    cb({
        playerData = playerData,
        jobHistory = jobHistory,
        ownedVehicles = ownedVehicles,
        stats = stats,
        availableJobs = availableJobs
    })
end)

-- Accept job event
RegisterNetEvent('zenith_delivery:acceptJob', function(jobId)
    local src = source
    local Player = QBCore.Functions.GetPlayer(src)
    if not Player then return end

    -- Check if player already has an active job
    if activeJobs[src] then
        TriggerClientEvent('ox_lib:notify', src, {
            title = 'Zenith Logistics',
            description = 'You already have an active delivery contract.',
            type = 'error'
        })
        return
    end

    -- Find the job
    local job = nil
    for i, availableJob in ipairs(availableJobs) do
        if availableJob.id == jobId then
            job = table.clone(availableJob)
            table.remove(availableJobs, i)
            break
        end
    end

    if not job then
        TriggerClientEvent('ox_lib:notify', src, {
            title = 'Zenith Logistics',
            description = 'This contract is no longer available.',
            type = 'error'
        })
        return
    end

    -- Check player level requirement
    local playerData = exports.zenith_delivery:GetPlayerData(Player.PlayerData.citizenid)
    if playerData.level < job.requiredLevel then
        TriggerClientEvent('ox_lib:notify', src, {
            title = 'Zenith Logistics',
            description = 'You need to be level ' .. job.requiredLevel .. ' for this contract.',
            type = 'error'
        })
        return
    end

    -- Store active job
    activeJobs[src] = {
        job = job,
        startTime = os.time(),
        currentStop = 1,
        citizenid = Player.PlayerData.citizenid
    }

    -- Notify client
    TriggerClientEvent('zenith_delivery:jobAccepted', src, job)
    TriggerClientEvent('zenith_delivery:updateAvailableJobs', -1, availableJobs)

    TriggerClientEvent('ox_lib:notify', src, {
        title = 'Zenith Logistics',
        description = 'Contract accepted! Head to the pickup location.',
        type = 'success'
    })
end)

-- Complete job stop event
RegisterNetEvent('zenith_delivery:completeStop', function(stopIndex)
    local src = source
    local Player = QBCore.Functions.GetPlayer(src)
    if not Player or not activeJobs[src] then return end

    local activeJob = activeJobs[src]
    local job = activeJob.job

    if stopIndex <= #job.stops then
        job.stops[stopIndex].completed = true
        activeJob.currentStop = stopIndex + 1

        -- Check if all stops completed
        local allCompleted = true
        for _, stop in ipairs(job.stops) do
            if not stop.completed then
                allCompleted = false
                break
            end
        end

        if allCompleted then
            -- Job completed
            CompleteJob(src, activeJob)
        else
            -- Update client with next stop
            TriggerClientEvent('zenith_delivery:updateJobProgress', src, job, activeJob.currentStop)
        end
    end
end)

-- Complete entire job
function CompleteJob(src, activeJob)
    local Player = QBCore.Functions.GetPlayer(src)
    if not Player then return end

    local job = activeJob.job
    local completionTime = os.time()
    local timeElapsed = completionTime - activeJob.startTime

    -- Calculate final payment and XP
    local finalPayment = job.payment
    local finalXP = job.xp

    -- Time bonus/penalty
    if job.timeBonus and timeElapsed < (job.estimatedTime * 60 * 0.8) then
        finalPayment = math.floor(finalPayment * 1.2)
        finalXP = math.floor(finalXP * 1.2)
    elseif job.timePenalty and timeElapsed > (job.estimatedTime * 60 * 1.5) then
        finalPayment = math.floor(finalPayment * 0.8)
        finalXP = math.floor(finalXP * 0.8)
    end

    -- Give rewards
    Player.Functions.AddMoney(Config.Banking.bankAccount, finalPayment)
    local leveledUp, newLevel = exports.zenith_delivery:AddPlayerXP(Player.PlayerData.citizenid, finalXP)

    -- Update player stats
    local playerData = exports.zenith_delivery:GetPlayerData(Player.PlayerData.citizenid)
    exports.zenith_delivery:UpdatePlayerData(Player.PlayerData.citizenid, {
        total_deliveries = playerData.total_deliveries + 1,
        total_earnings = playerData.total_earnings + finalPayment,
        last_job_time = os.date('%Y-%m-%d %H:%M:%S')
    })

    -- Save job history
    exports.zenith_delivery:SaveJobHistory(Player.PlayerData.citizenid, {
        type = job.type,
        status = 'completed',
        payment = finalPayment,
        xp_earned = finalXP,
        distance_traveled = 0, -- TODO: Calculate actual distance
        stops_completed = #job.stops,
        total_stops = #job.stops,
        vehicle_used = 'unknown' -- TODO: Get actual vehicle
    })

    -- Clear active job
    activeJobs[src] = nil

    -- Notify client
    TriggerClientEvent('zenith_delivery:jobCompleted', src, {
        payment = finalPayment,
        xp = finalXP,
        timeElapsed = timeElapsed,
        leveledUp = leveledUp,
        newLevel = newLevel
    })

    TriggerClientEvent('ox_lib:notify', src, {
        title = 'Zenith Logistics',
        description = 'Delivery completed! $' .. finalPayment .. ' deposited to your account.',
        type = 'success'
    })
end

-- Player disconnected cleanup
AddEventHandler('playerDropped', function()
    local src = source
    if activeJobs[src] then
        activeJobs[src] = nil
    end
end)
