Config.Vehicles = {
    ['phantom'] = {
        name = "Phantom",
        model = "phantom",
        category = "Heavy Duty",
        description = "The ultimate long-haul truck. Built for the toughest deliveries across San Andreas.",
        price = 0, -- Free starter vehicle
        requiredLevel = 1,
        stats = {
            acceleration = 65,
            topSpeed = 85,
            distance = 95, -- Fuel efficiency
            handling = 60
        },
        capabilities = {
            maxStops = 3,
            cargoTypes = {"general", "construction", "industrial"},
            trailerCompatible = true
        },
        image = "phantom.png"
    },
    
    ['mule'] = {
        name = "Mule",
        model = "mule",
        category = "Medium Duty",
        description = "Reliable medium-duty truck perfect for city deliveries and moderate cargo loads.",
        price = 75000,
        requiredLevel = 5,
        stats = {
            acceleration = 75,
            topSpeed = 90,
            distance = 80,
            handling = 85
        },
        capabilities = {
            maxStops = 5,
            cargoTypes = {"general", "retail", "food"},
            trailerCompatible = false
        },
        image = "mule.png"
    },
    
    ['pounder'] = {
        name = "Pounder",
        model = "pounder",
        category = "Heavy Duty",
        description = "Heavy-duty workhorse designed for maximum cargo capacity and durability.",
        price = 125000,
        requiredLevel = 10,
        stats = {
            acceleration = 55,
            topSpeed = 75,
            distance = 90,
            handling = 50
        },
        capabilities = {
            maxStops = 7,
            cargoTypes = {"general", "construction", "industrial", "hazmat"},
            trailerCompatible = true
        },
        image = "pounder.png"
    },
    
    ['benson'] = {
        name = "Benson",
        model = "benson",
        category = "Medium Duty",
        description = "Versatile delivery truck with excellent maneuverability for urban environments.",
        price = 95000,
        requiredLevel = 8,
        stats = {
            acceleration = 80,
            topSpeed = 95,
            distance = 75,
            handling = 90
        },
        capabilities = {
            maxStops = 4,
            cargoTypes = {"general", "retail", "food", "electronics"},
            trailerCompatible = false
        },
        image = "benson.png"
    },
    
    ['hauler'] = {
        name = "Hauler",
        model = "hauler",
        category = "Heavy Duty",
        description = "Premium long-haul truck with superior comfort and performance for experienced drivers.",
        price = 200000,
        requiredLevel = 20,
        stats = {
            acceleration = 70,
            topSpeed = 90,
            distance = 100,
            handling = 65
        },
        capabilities = {
            maxStops = 8,
            cargoTypes = {"general", "construction", "industrial", "hazmat", "luxury"},
            trailerCompatible = true
        },
        image = "hauler.png"
    },
    
    ['packer'] = {
        name = "Packer",
        model = "packer",
        category = "Specialized",
        description = "Specialized vehicle transport truck for high-value automotive deliveries.",
        price = 300000,
        requiredLevel = 30,
        stats = {
            acceleration = 60,
            topSpeed = 80,
            distance = 85,
            handling = 55
        },
        capabilities = {
            maxStops = 3,
            cargoTypes = {"automotive", "luxury"},
            trailerCompatible = true,
            specialCargo = true
        },
        image = "packer.png"
    },
    
    ['tanker'] = {
        name = "Tanker",
        model = "tanker",
        category = "Specialized",
        description = "Hazardous materials transport vehicle. Requires special certification and careful handling.",
        price = 250000,
        requiredLevel = 25,
        stats = {
            acceleration = 50,
            topSpeed = 70,
            distance = 95,
            handling = 45
        },
        capabilities = {
            maxStops = 2,
            cargoTypes = {"hazmat", "fuel", "chemicals"},
            trailerCompatible = false,
            specialCargo = true,
            hazardous = true
        },
        image = "tanker.png"
    },
    
    ['flatbed'] = {
        name = "Flatbed",
        model = "flatbed",
        category = "Specialized",
        description = "Open-bed truck perfect for construction materials and oversized cargo.",
        price = 150000,
        requiredLevel = 15,
        stats = {
            acceleration = 65,
            topSpeed = 85,
            distance = 80,
            handling = 70
        },
        capabilities = {
            maxStops = 5,
            cargoTypes = {"construction", "industrial", "oversized"},
            trailerCompatible = false,
            specialCargo = true
        },
        image = "flatbed.png"
    }
}

-- Vehicle Categories for UI filtering
Config.VehicleCategories = {
    "All",
    "Medium Duty",
    "Heavy Duty", 
    "Specialized"
}

-- Cargo Types and their descriptions
Config.CargoTypes = {
    general = {
        name = "General Freight",
        description = "Standard cargo and packages",
        color = "#4CAF50"
    },
    retail = {
        name = "Retail Goods",
        description = "Consumer products and merchandise",
        color = "#2196F3"
    },
    food = {
        name = "Food & Beverages",
        description = "Perishable goods requiring timely delivery",
        color = "#FF9800"
    },
    construction = {
        name = "Construction Materials",
        description = "Building supplies and heavy materials",
        color = "#795548"
    },
    industrial = {
        name = "Industrial Equipment",
        description = "Machinery and industrial components",
        color = "#607D8B"
    },
    electronics = {
        name = "Electronics",
        description = "Sensitive electronic equipment",
        color = "#9C27B0"
    },
    automotive = {
        name = "Automotive",
        description = "Vehicle parts and automobiles",
        color = "#F44336"
    },
    hazmat = {
        name = "Hazardous Materials",
        description = "Dangerous goods requiring special handling",
        color = "#FF5722"
    },
    fuel = {
        name = "Fuel & Petroleum",
        description = "Flammable liquids and fuel products",
        color = "#E91E63"
    },
    chemicals = {
        name = "Chemicals",
        description = "Industrial chemicals and compounds",
        color = "#3F51B5"
    },
    luxury = {
        name = "Luxury Goods",
        description = "High-value items requiring secure transport",
        color = "#FFD700"
    },
    oversized = {
        name = "Oversized Cargo",
        description = "Large items requiring special equipment",
        color = "#8BC34A"
    }
}
