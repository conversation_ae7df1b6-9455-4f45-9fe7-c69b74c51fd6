Config.JobTypes = {
    ['local_delivery'] = {
        name = "Local Delivery",
        description = "Short-distance deliveries within the city",
        icon = "truck",
        difficulty = 1,
        basePayment = {min = 5000, max = 8000},
        baseXP = {min = 50, max = 100},
        requiredLevel = 1,
        maxStops = 3,
        allowedVehicles = {"phantom", "mule", "benson"},
        cargoTypes = {"general", "retail", "food"},
        estimatedTime = 15, -- minutes
        riskLevel = "low"
    },
    
    ['city_freight'] = {
        name = "City Freight",
        description = "Medium-distance deliveries across Los Santos",
        icon = "shipping-fast",
        difficulty = 2,
        basePayment = {min = 8000, max = 12000},
        baseXP = {min = 75, max = 150},
        requiredLevel = 5,
        maxStops = 5,
        allowedVehicles = {"mule", "benson", "pounder"},
        cargoTypes = {"general", "retail", "electronics", "construction"},
        estimatedTime = 25,
        riskLevel = "low"
    },
    
    ['industrial_haul'] = {
        name = "Industrial Haul",
        description = "Heavy industrial equipment and materials",
        icon = "industry",
        difficulty = 3,
        basePayment = {min = 12000, max = 18000},
        baseXP = {min = 100, max = 200},
        requiredLevel = 10,
        maxStops = 4,
        allowedVehicles = {"pounder", "hauler", "flatbed"},
        cargoTypes = {"industrial", "construction", "oversized"},
        estimatedTime = 35,
        riskLevel = "medium"
    },
    
    ['long_haul'] = {
        name = "Long Haul",
        description = "Cross-state deliveries with trailer attachments",
        icon = "route",
        difficulty = 4,
        basePayment = {min = 18000, max = 25000},
        baseXP = {min = 150, max = 300},
        requiredLevel = 15,
        maxStops = 2,
        allowedVehicles = {"phantom", "hauler"},
        cargoTypes = {"general", "industrial", "automotive"},
        estimatedTime = 45,
        riskLevel = "medium",
        requiresTrailer = true
    },
    
    ['hazmat_transport'] = {
        name = "Hazmat Transport",
        description = "Dangerous goods requiring special certification",
        icon = "radiation",
        difficulty = 5,
        basePayment = {min = 25000, max = 35000},
        baseXP = {min = 200, max = 400},
        requiredLevel = 25,
        maxStops = 2,
        allowedVehicles = {"tanker"},
        cargoTypes = {"hazmat", "fuel", "chemicals"},
        estimatedTime = 40,
        riskLevel = "high",
        specialRequirements = true
    },
    
    ['luxury_delivery'] = {
        name = "Luxury Delivery",
        description = "High-value goods requiring secure transport",
        icon = "gem",
        difficulty = 4,
        basePayment = {min = 20000, max = 30000},
        baseXP = {min = 175, max = 350},
        requiredLevel = 20,
        maxStops = 3,
        allowedVehicles = {"hauler", "packer"},
        cargoTypes = {"luxury", "automotive", "electronics"},
        estimatedTime = 30,
        riskLevel = "high",
        highValue = true
    },
    
    ['construction_supply'] = {
        name = "Construction Supply",
        description = "Building materials for construction sites",
        icon = "hard-hat",
        difficulty = 3,
        basePayment = {min = 10000, max = 16000},
        baseXP = {min = 100, max = 200},
        requiredLevel = 8,
        maxStops = 6,
        allowedVehicles = {"pounder", "flatbed", "hauler"},
        cargoTypes = {"construction", "industrial"},
        estimatedTime = 40,
        riskLevel = "medium"
    },
    
    ['express_delivery'] = {
        name = "Express Delivery",
        description = "Time-sensitive deliveries with tight deadlines",
        icon = "clock",
        difficulty = 3,
        basePayment = {min = 15000, max = 22000},
        baseXP = {min = 125, max = 250},
        requiredLevel = 12,
        maxStops = 4,
        allowedVehicles = {"mule", "benson"},
        cargoTypes = {"general", "electronics", "food"},
        estimatedTime = 20,
        riskLevel = "medium",
        timeBonus = true,
        timePenalty = true
    }
}

-- Job generation settings
Config.JobGeneration = {
    maxActiveJobs = 8, -- Maximum jobs available at once
    refreshInterval = 300000, -- 5 minutes
    levelVariance = 3, -- Jobs can be ±3 levels from player level
    
    -- Job rarity weights
    rarityWeights = {
        common = 60,    -- 60% chance
        uncommon = 25,  -- 25% chance
        rare = 12,      -- 12% chance
        epic = 3        -- 3% chance
    },
    
    -- Payment multipliers by rarity
    rarityMultipliers = {
        common = 1.0,
        uncommon = 1.25,
        rare = 1.5,
        epic = 2.0
    }
}

-- Special job conditions
Config.SpecialConditions = {
    weather = {
        rain = {multiplier = 1.1, message = "Rain bonus"},
        storm = {multiplier = 1.2, message = "Storm hazard bonus"},
        fog = {multiplier = 1.15, message = "Low visibility bonus"}
    },
    
    timeOfDay = {
        night = {multiplier = 1.1, message = "Night shift bonus"},
        rush_hour = {multiplier = 1.05, message = "Rush hour bonus"}
    },
    
    distance = {
        short = {multiplier = 0.9, threshold = 1000}, -- Under 1km
        medium = {multiplier = 1.0, threshold = 5000}, -- 1-5km
        long = {multiplier = 1.2, threshold = 10000}, -- 5-10km
        extreme = {multiplier = 1.5, threshold = 999999} -- Over 10km
    }
}

-- Job failure reasons and penalties
Config.FailureReasons = {
    cargo_destroyed = {
        penalty = 0.5,
        message = "Cargo was destroyed during transport"
    },
    time_expired = {
        penalty = 0.25,
        message = "Delivery deadline was missed"
    },
    vehicle_destroyed = {
        penalty = 0.75,
        message = "Delivery vehicle was destroyed"
    },
    abandoned = {
        penalty = 0.3,
        message = "Job was abandoned"
    },
    hijacked = {
        penalty = 0.8,
        message = "Cargo was stolen during transport"
    }
}
