Config = {}

-- General Settings
Config.Debug = false
Config.TabletKey = 'F7'
Config.JobCooldown = 300000 -- 5 minutes between jobs (in ms)
Config.MaxActiveJobs = 1 -- Maximum active jobs per player

-- UI Settings
Config.UI = {
    enableAnimations = true,
    tabletAnimationTime = 500,
    notificationTime = 5000
}

-- Money & Banking
Config.Banking = {
    useBank = true, -- Money goes to bank instead of cash
    bankAccount = 'bank' -- Account type for QBOX
}

-- XP & Leveling
Config.Leveling = {
    startLevel = 1,
    maxLevel = 100,
    baseXP = 1000, -- XP required for level 2
    xpMultiplier = 1.2, -- XP requirement multiplier per level
    levelRewards = {
        [5] = {money = 10000, message = "Level 5 bonus!"},
        [10] = {money = 25000, message = "Level 10 bonus!"},
        [25] = {money = 50000, message = "Level 25 bonus!"},
        [50] = {money = 100000, message = "Level 50 bonus!"},
        [75] = {money = 200000, message = "Level 75 bonus!"},
        [100] = {money = 500000, message = "Max level reached!"}
    }
}

-- Job Failure System
Config.JobFailure = {
    enabled = true,
    cargoDestroyedPenalty = 0.5, -- 50% pay reduction
    abandonedJobPenalty = 0.25, -- 25% pay reduction
    timeoutMinutes = 60 -- Job auto-fails after 60 minutes
}

-- Dispatch Integration
Config.Dispatch = {
    enabled = true,
    hijackChance = 0.05, -- 5% chance for high value cargo
    policeAlert = true
}

-- Depot Locations
Config.Depots = {
    {
        name = "Fox Postal Depot",
        coords = vector3(-1040.0, -2024.0, 13.0),
        heading = 90.0,
        blip = {sprite = 478, color = 2, scale = 0.8}
    },
    {
        name = "Paleto Bay Depot",
        coords = vector3(-378.0, 6063.0, 31.0),
        heading = 315.0,
        blip = {sprite = 478, color = 2, scale = 0.8}
    },
    {
        name = "Port of Los Santos",
        coords = vector3(1180.0, -3113.0, 6.0),
        heading = 90.0,
        blip = {sprite = 478, color = 2, scale = 0.8}
    },
    {
        name = "Sandy Shores Depot",
        coords = vector3(1737.0, 3710.0, 34.0),
        heading = 20.0,
        blip = {sprite = 478, color = 2, scale = 0.8}
    },
    {
        name = "La Mesa Depot",
        coords = vector3(716.0, -1088.0, 22.0),
        heading = 0.0,
        blip = {sprite = 478, color = 2, scale = 0.8}
    }
}

-- Delivery Locations
Config.DeliveryLocations = {
    -- Businesses
    {name = "24/7 Supermarket", coords = vector3(25.0, -1347.0, 29.0)},
    {name = "LTD Gasoline", coords = vector3(1163.0, -323.0, 69.0)},
    {name = "Rob's Liquor", coords = vector3(1135.0, -982.0, 46.0)},
    {name = "Ammunation", coords = vector3(22.0, -1107.0, 29.0)},
    {name = "Binco", coords = vector3(72.0, -1399.0, 29.0)},
    
    -- Industrial Areas
    {name = "Maze Bank Arena", coords = vector3(-324.0, -1968.0, 67.0)},
    {name = "Los Santos Customs", coords = vector3(-362.0, -132.0, 38.0)},
    {name = "Benny's Original Motor Works", coords = vector3(-205.0, -1308.0, 31.0)},
    {name = "Davis Quartz", coords = vector3(2954.0, 2783.0, 41.0)},
    {name = "Palmer-Taylor Power Station", coords = vector3(2679.0, 1645.0, 24.0)},
    
    -- Residential Areas
    {name = "Mirror Park", coords = vector3(1010.0, -423.0, 65.0)},
    {name = "Vinewood Hills", coords = vector3(-1308.0, 449.0, 100.0)},
    {name = "Del Perro Heights", coords = vector3(-1447.0, -538.0, 34.0)},
    {name = "Vespucci Canals", coords = vector3(-1026.0, -1139.0, 2.0)},
    {name = "Little Seoul", coords = vector3(-635.0, -861.0, 25.0)}
}

-- Animation Settings
Config.Animations = {
    loading = {
        dict = "anim@amb@clubhouse@tutorial@bkr_tut_ig3@",
        anim = "machinic_loop_mechandplayer",
        duration = 5000
    },
    unloading = {
        dict = "anim@amb@clubhouse@tutorial@bkr_tut_ig3@",
        anim = "machinic_loop_mechandplayer",
        duration = 3000
    }
}

-- Notification Settings
Config.Notifications = {
    jobAccepted = {
        title = "Zenith Logistics",
        description = "Contract accepted! Head to the pickup location.",
        type = "success",
        duration = 5000
    },
    jobCompleted = {
        title = "Zenith Logistics",
        description = "Delivery completed successfully!",
        type = "success",
        duration = 5000
    },
    jobFailed = {
        title = "Zenith Logistics",
        description = "Delivery failed. Better luck next time.",
        type = "error",
        duration = 5000
    },
    levelUp = {
        title = "Zenith Logistics",
        description = "Level up! New opportunities await.",
        type = "success",
        duration = 7000
    }
}
