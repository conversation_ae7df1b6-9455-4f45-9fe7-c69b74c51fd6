-- Rewards and achievement system for Zenith Delivery

local QBCore = exports['qbx_core']:GetCoreObject()

-- Achievement definitions
local achievements = {
    first_delivery = {
        id = 'first_delivery',
        name = 'First Steps',
        description = 'Complete your first delivery',
        icon = 'truck',
        reward = {money = 5000, xp = 100},
        condition = function(playerData, stats) 
            return stats.completed_jobs >= 1 
        end
    },
    
    delivery_veteran = {
        id = 'delivery_veteran',
        name = 'Delivery Veteran',
        description = 'Complete 50 deliveries',
        icon = 'medal',
        reward = {money = 25000, xp = 500},
        condition = function(playerData, stats) 
            return stats.completed_jobs >= 50 
        end
    },
    
    high_roller = {
        id = 'high_roller',
        name = 'High Roller',
        description = 'Earn $1,000,000 from deliveries',
        icon = 'dollar-sign',
        reward = {money = 50000, xp = 1000},
        condition = function(playerData, stats) 
            return playerData.total_earnings >= 1000000 
        end
    },
    
    speed_demon = {
        id = 'speed_demon',
        name = 'Speed Demon',
        description = 'Complete 10 express deliveries with time bonus',
        icon = 'tachometer-alt',
        reward = {money = 15000, xp = 300},
        condition = function(playerData, stats) 
            -- This would need additional tracking
            return false 
        end
    },
    
    distance_driver = {
        id = 'distance_driver',
        name = 'Distance Driver',
        description = 'Drive 10,000 km in delivery vehicles',
        icon = 'road',
        reward = {money = 30000, xp = 750},
        condition = function(playerData, stats) 
            return playerData.total_distance >= 10000000 -- meters
        end
    },
    
    level_master = {
        id = 'level_master',
        name = 'Logistics Master',
        description = 'Reach level 50',
        icon = 'crown',
        reward = {money = 100000, xp = 0},
        condition = function(playerData, stats) 
            return playerData.level >= 50 
        end
    },
    
    perfect_record = {
        id = 'perfect_record',
        name = 'Perfect Record',
        description = 'Complete 25 deliveries without any failures',
        icon = 'star',
        reward = {money = 40000, xp = 800},
        condition = function(playerData, stats) 
            return stats.completed_jobs >= 25 and stats.failed_jobs == 0 
        end
    },
    
    hazmat_specialist = {
        id = 'hazmat_specialist',
        name = 'Hazmat Specialist',
        description = 'Complete 10 hazardous material deliveries',
        icon = 'radiation',
        reward = {money = 35000, xp = 600},
        condition = function(playerData, stats) 
            -- Would need additional tracking for job types
            return false 
        end
    },
    
    fleet_owner = {
        id = 'fleet_owner',
        name = 'Fleet Owner',
        description = 'Own 5 different delivery vehicles',
        icon = 'warehouse',
        reward = {money = 75000, xp = 1200},
        condition = function(playerData, stats, ownedVehicles) 
            return #ownedVehicles >= 5 
        end
    },
    
    night_owl = {
        id = 'night_owl',
        name = 'Night Owl',
        description = 'Complete 20 deliveries during night hours',
        icon = 'moon',
        reward = {money = 20000, xp = 400},
        condition = function(playerData, stats) 
            -- Would need additional tracking
            return false 
        end
    }
}

-- Check and award achievements
function CheckAchievements(citizenid)
    local playerData = exports.zenith_delivery:GetPlayerData(citizenid)
    local stats = exports.zenith_delivery:GetPlayerStats(citizenid)
    local ownedVehicles = exports.zenith_delivery:GetOwnedVehicles(citizenid)
    
    -- Get already unlocked achievements
    local unlockedAchievements = {}
    local result = MySQL.query.await('SELECT achievement_id FROM zenith_delivery_achievements WHERE citizenid = ?', {citizenid})
    if result then
        for _, row in ipairs(result) do
            unlockedAchievements[row.achievement_id] = true
        end
    end
    
    local newAchievements = {}
    
    -- Check each achievement
    for achievementId, achievement in pairs(achievements) do
        if not unlockedAchievements[achievementId] then
            if achievement.condition(playerData, stats, ownedVehicles) then
                -- Award achievement
                MySQL.insert('INSERT INTO zenith_delivery_achievements (citizenid, achievement_id) VALUES (?, ?)', {
                    citizenid, achievementId
                })
                
                table.insert(newAchievements, achievement)
                
                -- Give rewards
                local Player = exports.qbx_core:GetPlayerByCitizenId(citizenid)
                if Player then
                    if achievement.reward.money > 0 then
                        Player.Functions.AddMoney(Config.Banking.bankAccount, achievement.reward.money)
                    end
                    
                    if achievement.reward.xp > 0 then
                        exports.zenith_delivery:AddPlayerXP(citizenid, achievement.reward.xp)
                    end
                    
                    -- Notify player
                    TriggerClientEvent('zenith_delivery:achievementUnlocked', Player.PlayerData.source, achievement)
                    TriggerClientEvent('ox_lib:notify', Player.PlayerData.source, {
                        title = 'Achievement Unlocked!',
                        description = achievement.name .. ' - ' .. achievement.description,
                        type = 'success',
                        duration = 8000
                    })
                end
            end
        end
    end
    
    return newAchievements
end

-- Get player achievements
QBCore.Functions.CreateCallback('zenith_delivery:getAchievements', function(source, cb)
    local Player = QBCore.Functions.GetPlayer(source)
    if not Player then 
        cb({})
        return 
    end
    
    local result = MySQL.query.await('SELECT * FROM zenith_delivery_achievements WHERE citizenid = ?', {Player.PlayerData.citizenid})
    local unlockedAchievements = {}
    
    if result then
        for _, row in ipairs(result) do
            local achievement = achievements[row.achievement_id]
            if achievement then
                table.insert(unlockedAchievements, {
                    id = achievement.id,
                    name = achievement.name,
                    description = achievement.description,
                    icon = achievement.icon,
                    unlockedAt = row.unlocked_at
                })
            end
        end
    end
    
    -- Also send all available achievements with progress
    local allAchievements = {}
    local playerData = exports.zenith_delivery:GetPlayerData(Player.PlayerData.citizenid)
    local stats = exports.zenith_delivery:GetPlayerStats(Player.PlayerData.citizenid)
    local ownedVehicles = exports.zenith_delivery:GetOwnedVehicles(Player.PlayerData.citizenid)
    
    for achievementId, achievement in pairs(achievements) do
        local isUnlocked = false
        for _, unlocked in ipairs(unlockedAchievements) do
            if unlocked.id == achievementId then
                isUnlocked = true
                break
            end
        end
        
        table.insert(allAchievements, {
            id = achievement.id,
            name = achievement.name,
            description = achievement.description,
            icon = achievement.icon,
            reward = achievement.reward,
            unlocked = isUnlocked,
            progress = CalculateAchievementProgress(achievement, playerData, stats, ownedVehicles)
        })
    end
    
    cb({
        unlocked = unlockedAchievements,
        all = allAchievements
    })
end)

-- Calculate achievement progress
function CalculateAchievementProgress(achievement, playerData, stats, ownedVehicles)
    local progress = {current = 0, required = 1, percentage = 0}
    
    if achievement.id == 'first_delivery' then
        progress.current = stats.completed_jobs
        progress.required = 1
    elseif achievement.id == 'delivery_veteran' then
        progress.current = stats.completed_jobs
        progress.required = 50
    elseif achievement.id == 'high_roller' then
        progress.current = playerData.total_earnings
        progress.required = 1000000
    elseif achievement.id == 'distance_driver' then
        progress.current = playerData.total_distance
        progress.required = 10000000
    elseif achievement.id == 'level_master' then
        progress.current = playerData.level
        progress.required = 50
    elseif achievement.id == 'perfect_record' then
        progress.current = stats.completed_jobs
        progress.required = 25
    elseif achievement.id == 'fleet_owner' then
        progress.current = #ownedVehicles
        progress.required = 5
    end
    
    progress.percentage = math.min(100, math.floor((progress.current / progress.required) * 100))
    
    return progress
end

-- Daily/Weekly bonus system
local bonusRewards = {
    daily = {
        money = 10000,
        xp = 200,
        cooldown = 86400 -- 24 hours
    },
    weekly = {
        money = 50000,
        xp = 1000,
        cooldown = 604800 -- 7 days
    }
}

-- Claim daily bonus
RegisterNetEvent('zenith_delivery:claimDailyBonus', function()
    local src = source
    local Player = QBCore.Functions.GetPlayer(src)
    if not Player then return end
    
    local citizenid = Player.PlayerData.citizenid
    local currentTime = os.time()
    
    -- Check last claim time
    local result = MySQL.query.await('SELECT last_daily_bonus FROM zenith_delivery_players WHERE citizenid = ?', {citizenid})
    if result and result[1] and result[1].last_daily_bonus then
        local lastClaim = result[1].last_daily_bonus
        local timeDiff = currentTime - lastClaim
        
        if timeDiff < bonusRewards.daily.cooldown then
            local timeLeft = bonusRewards.daily.cooldown - timeDiff
            local hoursLeft = math.floor(timeLeft / 3600)
            local minutesLeft = math.floor((timeLeft % 3600) / 60)
            
            TriggerClientEvent('ox_lib:notify', src, {
                title = 'Zenith Logistics',
                description = 'Daily bonus available in ' .. hoursLeft .. 'h ' .. minutesLeft .. 'm',
                type = 'error'
            })
            return
        end
    end
    
    -- Give bonus
    Player.Functions.AddMoney(Config.Banking.bankAccount, bonusRewards.daily.money)
    exports.zenith_delivery:AddPlayerXP(citizenid, bonusRewards.daily.xp)
    
    -- Update last claim time
    MySQL.update('UPDATE zenith_delivery_players SET last_daily_bonus = ? WHERE citizenid = ?', {currentTime, citizenid})
    
    TriggerClientEvent('ox_lib:notify', src, {
        title = 'Daily Bonus Claimed!',
        description = '$' .. bonusRewards.daily.money .. ' and ' .. bonusRewards.daily.xp .. ' XP received',
        type = 'success'
    })
end)

-- Export achievement checking function
exports('CheckAchievements', CheckAchievements)

-- Hook into job completion to check achievements
AddEventHandler('zenith_delivery:jobCompleted', function(src, jobData)
    local Player = QBCore.Functions.GetPlayer(src)
    if Player then
        CheckAchievements(Player.PlayerData.citizenid)
    end
end)
