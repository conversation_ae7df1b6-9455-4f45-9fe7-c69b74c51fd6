-- Main client-side logic for Zenith Delivery System

local QBCore = exports['qbx_core']:GetCoreObject()
local PlayerData = {}
local isTabletOpen = false
local currentJob = nil
local currentVehicle = nil
local jobBlips = {}
local jobMarkers = {}

-- Initialize
CreateThread(function()
    while not QBCore do
        Wait(100)
    end
    
    PlayerData = QBCore.Functions.GetPlayerData()
    
    -- Register key mapping for tablet
    RegisterKeyMapping('zenith_tablet', 'Open Zenith Delivery Tablet', 'keyboard', Config.TabletKey)
    
    print("^2[Zenith Delivery]^7 Client initialized")
end)

-- Player loaded event
RegisterNetEvent('QBCore:Client:OnPlayerLoaded', function()
    PlayerData = QBCore.Functions.GetPlayerData()
    
    -- Request player data from server
    QBCore.Functions.TriggerCallback('zenith_delivery:getPlayerData', function(data)
        if data then
            SendNUIMessage({
                action = 'updatePlayerData',
                data = data
            })
        end
    end)
end)

-- Player job change event
RegisterNetEvent('QBCore:Client:OnJobUpdate', function(JobInfo)
    PlayerData.job = JobInfo
end)

-- Tablet key command
RegisterCommand('zenith_tablet', function()
    ToggleTablet()
end, false)

-- Toggle tablet UI
function ToggleTablet()
    if isTabletOpen then
        CloseTablet()
    else
        OpenTablet()
    end
end

-- Open tablet
function OpenTablet()
    if isTabletOpen then return end
    
    isTabletOpen = true
    
    -- Play tablet animation
    if Config.UI.enableAnimations then
        local ped = PlayerPedId()
        local tabletProp = CreateObject(GetHashKey('prop_cs_tablet'), 0, 0, 0, true, true, true)
        
        AttachEntityToEntity(tabletProp, ped, GetPedBoneIndex(ped, 57005), 0.17, 0.10, -0.13, 20.0, 180.0, 180.0, true, true, false, true, 1, true)
        
        -- Store prop for cleanup
        currentTabletProp = tabletProp
        
        -- Play animation
        RequestAnimDict("amb@world_human_seat_wall_tablet@female@base")
        while not HasAnimDictLoaded("amb@world_human_seat_wall_tablet@female@base") do
            Wait(100)
        end
        TaskPlayAnim(ped, "amb@world_human_seat_wall_tablet@female@base", "base", 8.0, -8.0, -1, 50, 0, false, false, false)
    end
    
    -- Request fresh data
    QBCore.Functions.TriggerCallback('zenith_delivery:getPlayerData', function(data)
        if data then
            -- Open NUI
            SetNuiFocus(true, true)
            SendNUIMessage({
                action = 'openTablet',
                data = data
            })
        end
    end)
end

-- Close tablet
function CloseTablet()
    if not isTabletOpen then return end
    
    isTabletOpen = false
    
    -- Close NUI
    SetNuiFocus(false, false)
    SendNUIMessage({
        action = 'closeTablet'
    })
    
    -- Clean up animation and prop
    if Config.UI.enableAnimations then
        local ped = PlayerPedId()
        ClearPedTasks(ped)
        
        if currentTabletProp then
            DeleteObject(currentTabletProp)
            currentTabletProp = nil
        end
    end
end

-- NUI Callbacks
RegisterNUICallback('closeTablet', function(data, cb)
    CloseTablet()
    cb('ok')
end)

RegisterNUICallback('acceptJob', function(data, cb)
    if not data.jobId then
        cb('error')
        return
    end
    
    -- Check if player has correct vehicle
    local playerVehicle = GetVehiclePedIsIn(PlayerPedId(), false)
    if playerVehicle == 0 then
        QBCore.Functions.Notify('You need to be in a delivery vehicle to accept this contract.', 'error')
        cb('error')
        return
    end
    
    local vehicleModel = string.lower(GetDisplayNameFromVehicleModel(GetEntityModel(playerVehicle)))
    
    -- Trigger server event
    TriggerServerEvent('zenith_delivery:acceptJob', data.jobId)
    
    cb('ok')
end)

RegisterNUICallback('purchaseVehicle', function(data, cb)
    if not data.vehicleModel then
        cb('error')
        return
    end
    
    TriggerServerEvent('zenith_delivery:purchaseVehicle', data.vehicleModel)
    cb('ok')
end)

RegisterNUICallback('abandonJob', function(data, cb)
    if currentJob then
        TriggerServerEvent('zenith_delivery:abandonJob')
        CleanupJob()
    end
    cb('ok')
end)

RegisterNUICallback('claimDailyBonus', function(data, cb)
    TriggerServerEvent('zenith_delivery:claimDailyBonus')
    cb('ok')
end)

-- Job accepted event
RegisterNetEvent('zenith_delivery:jobAccepted', function(job)
    currentJob = job
    CloseTablet()
    
    -- Create job markers and blips
    CreateJobMarkers(job)
    
    QBCore.Functions.Notify('Contract accepted! Head to the pickup location marked on your GPS.', 'success')
end)

-- Job completed event
RegisterNetEvent('zenith_delivery:jobCompleted', function(completionData)
    CleanupJob()
    
    -- Show completion screen
    SendNUIMessage({
        action = 'showJobCompletion',
        data = completionData
    })
    
    SetNuiFocus(true, true)
    
    -- Auto-close after 10 seconds
    SetTimeout(10000, function()
        SendNUIMessage({action = 'hideJobCompletion'})
        SetNuiFocus(false, false)
    end)
end)

-- Job failed event
RegisterNetEvent('zenith_delivery:jobFailed', function(failureData)
    CleanupJob()
    
    QBCore.Functions.Notify('Delivery failed: ' .. failureData.reason, 'error')
end)

-- Level up event
RegisterNetEvent('zenith_delivery:levelUp', function(newLevel, oldLevel)
    -- Show level up notification
    SendNUIMessage({
        action = 'showLevelUp',
        data = {
            newLevel = newLevel,
            oldLevel = oldLevel
        }
    })
    
    QBCore.Functions.Notify('Level up! You are now level ' .. newLevel, 'success')
end)

-- Achievement unlocked event
RegisterNetEvent('zenith_delivery:achievementUnlocked', function(achievement)
    SendNUIMessage({
        action = 'showAchievement',
        data = achievement
    })
end)

-- Update available jobs
RegisterNetEvent('zenith_delivery:updateAvailableJobs', function(jobs)
    SendNUIMessage({
        action = 'updateAvailableJobs',
        data = jobs
    })
end)

-- Update player data
RegisterNetEvent('zenith_delivery:updatePlayerData', function(data)
    SendNUIMessage({
        action = 'updatePlayerData',
        data = data
    })
end)

-- Update owned vehicles
RegisterNetEvent('zenith_delivery:updateOwnedVehicles', function(vehicles)
    SendNUIMessage({
        action = 'updateOwnedVehicles',
        data = vehicles
    })
end)

-- Create job markers and blips
function CreateJobMarkers(job)
    CleanupJobMarkers()
    
    if not job.stops or #job.stops == 0 then return end
    
    -- Create blip for first stop (pickup)
    local firstStop = job.stops[1]
    local blip = AddBlipForCoord(firstStop.coords.x, firstStop.coords.y, firstStop.coords.z)
    SetBlipSprite(blip, 478) -- Delivery truck icon
    SetBlipColour(blip, 2) -- Green
    SetBlipScale(blip, 1.0)
    SetBlipAsShortRange(blip, false)
    BeginTextCommandSetBlipName("STRING")
    AddTextComponentString("Pickup Location")
    EndTextCommandSetBlipName(blip)
    SetBlipRoute(blip, true)
    
    table.insert(jobBlips, blip)
    
    -- Create marker thread
    CreateThread(function()
        while currentJob do
            local playerCoords = GetEntityCoords(PlayerPedId())
            
            for i, stop in ipairs(currentJob.stops) do
                if not stop.completed then
                    local distance = #(playerCoords - stop.coords)
                    
                    if distance < 100.0 then
                        -- Draw marker
                        DrawMarker(1, stop.coords.x, stop.coords.y, stop.coords.z - 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 3.0, 3.0, 1.0, 0, 255, 0, 100, false, true, 2, false, nil, nil, false)
                        
                        if distance < 5.0 then
                            -- Show interaction text
                            local text = stop.type == "pickup" and "Press [E] to load cargo" or "Press [E] to deliver cargo"
                            DrawText3D(stop.coords.x, stop.coords.y, stop.coords.z + 1.0, text)
                            
                            if IsControlJustPressed(0, 38) then -- E key
                                HandleStopInteraction(i, stop)
                            end
                        end
                    end
                    
                    break -- Only show marker for current stop
                end
            end
            
            Wait(0)
        end
    end)
end

-- Handle stop interaction
function HandleStopInteraction(stopIndex, stop)
    local ped = PlayerPedId()
    local vehicle = GetVehiclePedIsIn(ped, false)
    
    if vehicle == 0 then
        QBCore.Functions.Notify('You need to be in your delivery vehicle.', 'error')
        return
    end
    
    -- Play loading/unloading animation
    local animDict = stop.type == "pickup" and Config.Animations.loading.dict or Config.Animations.unloading.dict
    local animName = stop.type == "pickup" and Config.Animations.loading.anim or Config.Animations.unloading.anim
    local duration = stop.type == "pickup" and Config.Animations.loading.duration or Config.Animations.unloading.duration
    
    RequestAnimDict(animDict)
    while not HasAnimDictLoaded(animDict) do
        Wait(100)
    end
    
    TaskPlayAnim(ped, animDict, animName, 8.0, -8.0, duration, 1, 0, false, false, false)
    
    -- Show progress bar
    QBCore.Functions.Progressbar("delivery_action", stop.type == "pickup" and "Loading cargo..." or "Delivering cargo...", duration, false, true, {
        disableMovement = true,
        disableCarMovement = true,
        disableMouse = false,
        disableCombat = true,
    }, {}, {}, {}, function() -- Done
        ClearPedTasks(ped)
        TriggerServerEvent('zenith_delivery:completeStop', stopIndex)
        
        -- Update local job state
        currentJob.stops[stopIndex].completed = true
        
        -- Update blip for next stop
        UpdateJobBlips()
        
        QBCore.Functions.Notify(stop.type == "pickup" and "Cargo loaded successfully!" or "Cargo delivered successfully!", 'success')
    end, function() -- Cancel
        ClearPedTasks(ped)
        QBCore.Functions.Notify("Action cancelled.", 'error')
    end)
end

-- Update job blips for next stop
function UpdateJobBlips()
    -- Clear existing blips
    for _, blip in ipairs(jobBlips) do
        RemoveBlip(blip)
    end
    jobBlips = {}

    if not currentJob then return end

    -- Find next incomplete stop
    for i, stop in ipairs(currentJob.stops) do
        if not stop.completed then
            local blip = AddBlipForCoord(stop.coords.x, stop.coords.y, stop.coords.z)
            SetBlipSprite(blip, stop.type == "pickup" and 478 or 280)
            SetBlipColour(blip, stop.type == "pickup" and 2 or 3)
            SetBlipScale(blip, 1.0)
            SetBlipAsShortRange(blip, false)
            BeginTextCommandSetBlipName("STRING")
            AddTextComponentString(stop.type == "pickup" and "Pickup Location" or "Delivery Location")
            EndTextCommandSetBlipName(blip)
            SetBlipRoute(blip, true)

            table.insert(jobBlips, blip)
            break
        end
    end
end

-- Cleanup job markers and blips
function CleanupJobMarkers()
    for _, blip in ipairs(jobBlips) do
        RemoveBlip(blip)
    end
    jobBlips = {}

    for _, marker in ipairs(jobMarkers) do
        -- Markers are drawn in real-time, no cleanup needed
    end
    jobMarkers = {}
end

-- Cleanup entire job
function CleanupJob()
    currentJob = nil
    CleanupJobMarkers()
end

-- Draw 3D text
function DrawText3D(x, y, z, text)
    local onScreen, _x, _y = World3dToScreen2d(x, y, z)
    local px, py, pz = table.unpack(GetGameplayCamCoords())

    SetTextScale(0.35, 0.35)
    SetTextFont(4)
    SetTextProportional(1)
    SetTextColour(255, 255, 255, 215)
    SetTextEntry("STRING")
    SetTextCentre(1)
    AddTextComponentString(text)
    DrawText(_x, _y)

    local factor = (string.len(text)) / 370
    DrawRect(_x, _y + 0.0125, 0.015 + factor, 0.03, 41, 11, 41, 68)
end

-- Vehicle check for job compatibility
function IsVehicleCompatibleWithJob(vehicle, job)
    if not vehicle or not job then return false end

    local vehicleModel = string.lower(GetDisplayNameFromVehicleModel(GetEntityModel(vehicle)))

    for _, allowedVehicle in ipairs(job.allowedVehicles) do
        if allowedVehicle == vehicleModel then
            return true
        end
    end

    return false
end

-- Resource cleanup
AddEventHandler('onResourceStop', function(resourceName)
    if GetCurrentResourceName() == resourceName then
        CleanupJob()

        if isTabletOpen then
            CloseTablet()
        end

        if currentTabletProp then
            DeleteObject(currentTabletProp)
        end
    end
end)
