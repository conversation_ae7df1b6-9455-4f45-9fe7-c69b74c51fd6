-- Vehicle management for Zenith Delivery System

local QBCore = exports['qbx_core']:GetCoreObject()
local spawnedVehicles = {}
local vehicleBlips = {}

-- Vehicle spawning with QBOX garage integration
RegisterNetEvent('zenith_delivery:spawnVehicleFromGarage', function(vehicleData)
    if not vehicleData or not vehicleData.model then
        QBCore.Functions.Notify('Invalid vehicle data.', 'error')
        return
    end
    
    local ped = PlayerPedId()
    local coords = GetEntityCoords(ped)
    
    -- Find nearest garage or suitable spawn location
    local spawnLocation = FindNearestGarageSpawn(coords)
    if not spawnLocation then
        QBCore.Functions.Notify('No suitable spawn location found.', 'error')
        return
    end
    
    -- Request model
    local modelHash = GetHashKey(vehicleData.model)
    RequestModel(modelHash)
    
    local timeout = 0
    while not HasModelLoaded(modelHash) and timeout < 10000 do
        Wait(100)
        timeout = timeout + 100
    end
    
    if not HasModelLoaded(modelHash) then
        QBCore.Functions.Notify('Failed to load vehicle model.', 'error')
        return
    end
    
    -- Create vehicle
    local vehicle = CreateVehicle(modelHash, spawnLocation.x, spawnLocation.y, spawnLocation.z, spawnLocation.w, true, false)
    
    if not vehicle or vehicle == 0 then
        QBCore.Functions.Notify('Failed to spawn vehicle.', 'error')
        SetModelAsNoLongerNeeded(modelHash)
        return
    end
    
    -- Apply vehicle properties from QBOX data
    if vehicleData.mods then
        QBCore.Functions.SetVehicleProperties(vehicle, vehicleData.mods)
    end
    
    -- Set vehicle properties
    SetVehicleNumberPlateText(vehicle, vehicleData.plate or "ZENITH" .. math.random(10, 99))
    SetEntityAsMissionEntity(vehicle, true, true)
    SetVehicleEngineOn(vehicle, true, true, false)
    SetVehicleFuelLevel(vehicle, vehicleData.fuel or 100.0)
    SetVehicleEngineHealth(vehicle, vehicleData.engine or 1000.0)
    SetVehicleBodyHealth(vehicle, vehicleData.body or 1000.0)
    
    -- Add to spawned vehicles tracking
    spawnedVehicles[vehicle] = {
        model = vehicleData.model,
        plate = vehicleData.plate,
        citizenid = vehicleData.citizenid
    }
    
    -- Create vehicle blip
    local blip = AddBlipForEntity(vehicle)
    SetBlipSprite(blip, 477)
    SetBlipColour(blip, 2)
    SetBlipScale(blip, 0.8)
    BeginTextCommandSetBlipName("STRING")
    AddTextComponentString("Delivery Vehicle")
    EndTextCommandSetBlipName(blip)
    vehicleBlips[vehicle] = blip
    
    -- Put player in vehicle
    TaskWarpPedIntoVehicle(ped, vehicle, -1)
    currentVehicle = vehicle
    
    QBCore.Functions.Notify('Vehicle spawned successfully!', 'success')
    SetModelAsNoLongerNeeded(modelHash)
end)

-- Find nearest garage spawn location
function FindNearestGarageSpawn(playerCoords)
    local garageLocations = {
        {x = 215.9, y = -810.1, z = 30.7, w = 160.0}, -- Mission Row Garage
        {x = -340.7, y = -874.6, z = 31.3, w = 170.0}, -- Pillbox Garage
        {x = 69.8, y = 12.6, z = 69.2, w = 160.0}, -- Alta Street Garage
        {x = -796.6, y = -2024.8, z = 8.9, w = 135.0}, -- Airport Garage
        {x = 1737.6, y = 3710.2, z = 34.1, w = 20.0}, -- Sandy Shores Garage
        {x = -73.3, y = 6434.5, z = 31.4, w = 45.0}, -- Paleto Bay Garage
    }
    
    local closestGarage = nil
    local closestDistance = math.huge
    
    for _, garage in ipairs(garageLocations) do
        local distance = #(playerCoords - vector3(garage.x, garage.y, garage.z))
        if distance < closestDistance then
            closestDistance = distance
            closestGarage = garage
        end
    end
    
    if closestGarage and closestDistance < 1000.0 then -- Within 1km
        return FindClearSpawnPoint(closestGarage)
    end
    
    -- Fallback to player area
    return FindClearSpawnPoint({x = playerCoords.x, y = playerCoords.y, z = playerCoords.z, w = GetEntityHeading(PlayerPedId())})
end

-- Find clear spawn point near location
function FindClearSpawnPoint(location)
    local attempts = 0
    local maxAttempts = 20
    
    while attempts < maxAttempts do
        local offsetX = math.random(-15, 15)
        local offsetY = math.random(-15, 15)
        local testCoords = vector3(location.x + offsetX, location.y + offsetY, location.z)
        
        local groundZ = GetGroundZFor_3dCoord(testCoords.x, testCoords.y, testCoords.z + 10.0, false)
        local finalCoords = vector4(testCoords.x, testCoords.y, groundZ, location.w)
        
        if IsSpawnPointClear(finalCoords, 4.0) then
            return finalCoords
        end
        
        attempts = attempts + 1
    end
    
    -- Return original location as fallback
    return vector4(location.x, location.y, location.z, location.w)
end

-- Vehicle return to garage
RegisterNetEvent('zenith_delivery:returnVehicleToGarage', function()
    local ped = PlayerPedId()
    local vehicle = GetVehiclePedIsIn(ped, false)
    
    if vehicle == 0 then
        QBCore.Functions.Notify('You must be in a vehicle to return it to the garage.', 'error')
        return
    end
    
    if not spawnedVehicles[vehicle] then
        QBCore.Functions.Notify('This is not a delivery vehicle.', 'error')
        return
    end
    
    -- Check if vehicle is in a job
    if currentJob then
        QBCore.Functions.Notify('Cannot return vehicle during an active delivery.', 'error')
        return
    end
    
    -- Get vehicle data
    local vehicleData = spawnedVehicles[vehicle]
    local plate = GetVehicleNumberPlateText(vehicle)
    local fuel = GetVehicleFuelLevel(vehicle)
    local engine = GetVehicleEngineHealth(vehicle)
    local body = GetVehicleBodyHealth(vehicle)
    
    -- Update vehicle in database
    local mods = QBCore.Functions.GetVehicleProperties(vehicle)
    
    TriggerServerEvent('zenith_delivery:updateVehicleInGarage', {
        plate = plate,
        fuel = fuel,
        engine = engine,
        body = body,
        mods = mods
    })
    
    -- Remove blip
    if vehicleBlips[vehicle] then
        RemoveBlip(vehicleBlips[vehicle])
        vehicleBlips[vehicle] = nil
    end
    
    -- Remove from tracking
    spawnedVehicles[vehicle] = nil
    
    -- Delete vehicle
    DeleteEntity(vehicle)
    
    if vehicle == currentVehicle then
        currentVehicle = nil
    end
    
    QBCore.Functions.Notify('Vehicle returned to garage.', 'success')
end)

-- Vehicle damage and maintenance system
CreateThread(function()
    while true do
        Wait(10000) -- Check every 10 seconds
        
        for vehicle, data in pairs(spawnedVehicles) do
            if DoesEntityExist(vehicle) then
                local engine = GetVehicleEngineHealth(vehicle)
                local body = GetVehicleBodyHealth(vehicle)
                
                -- Update vehicle condition in our tracking
                if data.lastEngine and data.lastBody then
                    local engineDamage = data.lastEngine - engine
                    local bodyDamage = data.lastBody - body
                    
                    -- Notify about significant damage
                    if engineDamage > 100 or bodyDamage > 100 then
                        local ped = PlayerPedId()
                        local playerVehicle = GetVehiclePedIsIn(ped, false)
                        
                        if playerVehicle == vehicle then
                            QBCore.Functions.Notify('Vehicle sustained damage!', 'error')
                        end
                    end
                end
                
                data.lastEngine = engine
                data.lastBody = body
                
                -- Check for critical damage
                if engine < 200 or body < 200 then
                    local ped = PlayerPedId()
                    local playerVehicle = GetVehiclePedIsIn(ped, false)
                    
                    if playerVehicle == vehicle then
                        QBCore.Functions.Notify('Critical vehicle damage! Seek repairs immediately!', 'error')
                    end
                end
            else
                -- Vehicle was destroyed, clean up
                if vehicleBlips[vehicle] then
                    RemoveBlip(vehicleBlips[vehicle])
                    vehicleBlips[vehicle] = nil
                end
                spawnedVehicles[vehicle] = nil
            end
        end
    end
end)

-- Vehicle statistics tracking
function UpdateVehicleStats(vehicle, distance)
    if not spawnedVehicles[vehicle] then return end
    
    local data = spawnedVehicles[vehicle]
    data.totalDistance = (data.totalDistance or 0) + distance
    data.totalJobs = (data.totalJobs or 0) + (currentJob and 1 or 0)
    
    -- Update server-side stats periodically
    if data.totalDistance and data.totalDistance > (data.lastReportedDistance or 0) + 1000 then -- Every 1km
        TriggerServerEvent('zenith_delivery:updateVehicleStats', {
            plate = GetVehicleNumberPlateText(vehicle),
            distance = data.totalDistance,
            jobs = data.totalJobs
        })
        data.lastReportedDistance = data.totalDistance
    end
end

-- Vehicle fuel management
CreateThread(function()
    while true do
        Wait(60000) -- Check every minute
        
        for vehicle, data in pairs(spawnedVehicles) do
            if DoesEntityExist(vehicle) then
                local fuel = GetVehicleFuelLevel(vehicle)
                
                -- Warn about low fuel
                if fuel < 20.0 then
                    local ped = PlayerPedId()
                    local playerVehicle = GetVehiclePedIsIn(ped, false)
                    
                    if playerVehicle == vehicle then
                        QBCore.Functions.Notify('Low fuel warning! Find a gas station.', 'error')
                    end
                end
                
                -- Emergency fuel if completely empty during job
                if fuel <= 0 and currentJob and GetVehiclePedIsIn(PlayerPedId(), false) == vehicle then
                    QBCore.Functions.Notify('Emergency fuel added to complete delivery.', 'inform')
                    SetVehicleFuelLevel(vehicle, 25.0)
                end
            end
        end
    end
end)

-- Cleanup on resource stop
AddEventHandler('onResourceStop', function(resourceName)
    if GetCurrentResourceName() == resourceName then
        -- Clean up all spawned vehicles
        for vehicle, data in pairs(spawnedVehicles) do
            if DoesEntityExist(vehicle) then
                DeleteEntity(vehicle)
            end
        end
        
        -- Clean up blips
        for vehicle, blip in pairs(vehicleBlips) do
            RemoveBlip(blip)
        end
        
        spawnedVehicles = {}
        vehicleBlips = {}
    end
end)

-- Export functions
exports('GetCurrentVehicle', function()
    return currentVehicle
end)

exports('IsVehicleSpawned', function(plate)
    for vehicle, data in pairs(spawnedVehicles) do
        if data.plate == plate then
            return true, vehicle
        end
    end
    return false, nil
end)
