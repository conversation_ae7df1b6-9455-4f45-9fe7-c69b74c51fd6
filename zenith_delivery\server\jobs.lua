-- Job management and progression system

local QBCore = exports['qbx_core']:GetCoreObject()

-- Vehicle purchase event
RegisterNetEvent('zenith_delivery:purchaseVehicle', function(vehicleModel)
    local src = source
    local Player = QBCore.Functions.GetPlayer(src)
    if not Player then return end
    
    local vehicleData = Config.Vehicles[vehicleModel]
    if not vehicleData then
        TriggerClientEvent('ox_lib:notify', src, {
            title = 'Zenith Logistics',
            description = 'Invalid vehicle selection.',
            type = 'error'
        })
        return
    end
    
    -- Check player level
    local playerData = exports.zenith_delivery:GetPlayerData(Player.PlayerData.citizenid)
    if playerData.level < vehicleData.requiredLevel then
        TriggerClientEvent('ox_lib:notify', src, {
            title = 'Zenith Logistics',
            description = 'You need to be level ' .. vehicleData.requiredLevel .. ' to purchase this vehicle.',
            type = 'error'
        })
        return
    end
    
    -- Check if player already owns this vehicle
    local ownedVehicles = exports.zenith_delivery:GetOwnedVehicles(Player.PlayerData.citizenid)
    for _, vehicle in ipairs(ownedVehicles) do
        if vehicle.vehicle_model == vehicleModel then
            TriggerClientEvent('ox_lib:notify', src, {
                title = 'Zenith Logistics',
                description = 'You already own this vehicle.',
                type = 'error'
            })
            return
        end
    end
    
    -- Check if player has enough money
    if vehicleData.price > 0 then
        local playerMoney = Player.PlayerData.money[Config.Banking.bankAccount] or 0
        if playerMoney < vehicleData.price then
            TriggerClientEvent('ox_lib:notify', src, {
                title = 'Zenith Logistics',
                description = 'Insufficient funds. You need $' .. vehicleData.price .. '.',
                type = 'error'
            })
            return
        end
        
        -- Remove money
        Player.Functions.RemoveMoney(Config.Banking.bankAccount, vehicleData.price)
    end
    
    -- Add vehicle to QBOX garage system
    local vehicleProps = {
        model = vehicleModel,
        plate = GenerateRandomPlate(),
        garage = 'pillboxgarage', -- Default garage
        fuel = 100,
        engine = 1000.0,
        body = 1000.0,
        state = 0 -- Stored in garage
    }
    
    -- Insert into QBOX vehicles table
    MySQL.insert('INSERT INTO player_vehicles (license, citizenid, vehicle, hash, mods, plate, garage, state) VALUES (?, ?, ?, ?, ?, ?, ?, ?)', {
        Player.PlayerData.license,
        Player.PlayerData.citizenid,
        vehicleModel,
        GetHashKey(vehicleModel),
        json.encode(vehicleProps),
        vehicleProps.plate,
        vehicleProps.garage,
        vehicleProps.state
    })
    
    -- Add to our tracking table
    exports.zenith_delivery:PurchaseVehicle(Player.PlayerData.citizenid, vehicleModel)
    
    TriggerClientEvent('ox_lib:notify', src, {
        title = 'Zenith Logistics',
        description = 'Vehicle purchased successfully! Check your garage.',
        type = 'success'
    })
    
    -- Update client data
    local updatedData = exports.zenith_delivery:GetOwnedVehicles(Player.PlayerData.citizenid)
    TriggerClientEvent('zenith_delivery:updateOwnedVehicles', src, updatedData)
end)

-- Generate random license plate
function GenerateRandomPlate()
    local chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
    local plate = ""
    
    for i = 1, 8 do
        local randomIndex = math.random(1, #chars)
        plate = plate .. chars:sub(randomIndex, randomIndex)
    end
    
    return plate
end

-- Get available jobs for player level
QBCore.Functions.CreateCallback('zenith_delivery:getAvailableJobs', function(source, cb)
    local Player = QBCore.Functions.GetPlayer(source)
    if not Player then 
        cb({})
        return 
    end
    
    local playerData = exports.zenith_delivery:GetPlayerData(Player.PlayerData.citizenid)
    local availableJobs = {}
    
    -- Filter jobs based on player level
    for _, job in ipairs(availableJobs) do
        if job.requiredLevel <= playerData.level + Config.JobGeneration.levelVariance then
            table.insert(availableJobs, job)
        end
    end
    
    cb(availableJobs)
end)

-- Check vehicle compatibility for job
QBCore.Functions.CreateCallback('zenith_delivery:checkVehicleCompatibility', function(source, cb, jobId, vehicleModel)
    local Player = QBCore.Functions.GetPlayer(source)
    if not Player then 
        cb(false)
        return 
    end
    
    -- Find the job
    local job = nil
    for _, availableJob in ipairs(availableJobs) do
        if availableJob.id == jobId then
            job = availableJob
            break
        end
    end
    
    if not job then
        cb(false)
        return
    end
    
    -- Check if vehicle is allowed for this job
    for _, allowedVehicle in ipairs(job.allowedVehicles) do
        if allowedVehicle == vehicleModel then
            cb(true)
            return
        end
    end
    
    cb(false)
end)

-- Fail job event
RegisterNetEvent('zenith_delivery:failJob', function(reason)
    local src = source
    local Player = QBCore.Functions.GetPlayer(src)
    if not Player or not activeJobs[src] then return end
    
    local activeJob = activeJobs[src]
    local job = activeJob.job
    
    -- Calculate penalty
    local penalty = Config.FailureReasons[reason]
    if not penalty then
        penalty = Config.FailureReasons.abandoned
    end
    
    local penaltyAmount = math.floor(job.payment * penalty.penalty)
    
    -- Save failed job to history
    exports.zenith_delivery:SaveJobHistory(Player.PlayerData.citizenid, {
        type = job.type,
        status = 'failed',
        payment = 0,
        xp_earned = 0,
        distance_traveled = 0,
        stops_completed = activeJob.currentStop - 1,
        total_stops = #job.stops,
        vehicle_used = 'unknown',
        failure_reason = penalty.message
    })
    
    -- Clear active job
    activeJobs[src] = nil
    
    -- Notify client
    TriggerClientEvent('zenith_delivery:jobFailed', src, {
        reason = penalty.message,
        penalty = penaltyAmount
    })
    
    TriggerClientEvent('ox_lib:notify', src, {
        title = 'Zenith Logistics',
        description = 'Delivery failed: ' .. penalty.message,
        type = 'error'
    })
end)

-- Abandon job event
RegisterNetEvent('zenith_delivery:abandonJob', function()
    local src = source
    local Player = QBCore.Functions.GetPlayer(src)
    if not Player or not activeJobs[src] then return end
    
    local activeJob = activeJobs[src]
    local job = activeJob.job
    
    -- Save abandoned job to history
    exports.zenith_delivery:SaveJobHistory(Player.PlayerData.citizenid, {
        type = job.type,
        status = 'abandoned',
        payment = 0,
        xp_earned = 0,
        distance_traveled = 0,
        stops_completed = activeJob.currentStop - 1,
        total_stops = #job.stops,
        vehicle_used = 'unknown',
        failure_reason = 'Job abandoned by player'
    })
    
    -- Clear active job
    activeJobs[src] = nil
    
    TriggerClientEvent('ox_lib:notify', src, {
        title = 'Zenith Logistics',
        description = 'Delivery contract abandoned.',
        type = 'inform'
    })
end)

-- Get player leaderboard
QBCore.Functions.CreateCallback('zenith_delivery:getLeaderboard', function(source, cb, category)
    category = category or 'level'
    
    local query = ''
    if category == 'level' then
        query = 'SELECT citizenid, level, xp FROM zenith_delivery_players ORDER BY level DESC, xp DESC LIMIT 10'
    elseif category == 'earnings' then
        query = 'SELECT citizenid, total_earnings FROM zenith_delivery_players ORDER BY total_earnings DESC LIMIT 10'
    elseif category == 'deliveries' then
        query = 'SELECT citizenid, total_deliveries FROM zenith_delivery_players ORDER BY total_deliveries DESC LIMIT 10'
    else
        cb({})
        return
    end
    
    local result = MySQL.query.await(query)
    if result then
        -- Get player names
        for i, row in ipairs(result) do
            local Player = QBCore.Functions.GetPlayerByCitizenId(row.citizenid)
            if Player then
                row.name = Player.PlayerData.charinfo.firstname .. ' ' .. Player.PlayerData.charinfo.lastname
            else
                -- Get from offline data
                local offlineData = MySQL.query.await('SELECT charinfo FROM players WHERE citizenid = ?', {row.citizenid})
                if offlineData and offlineData[1] then
                    local charinfo = json.decode(offlineData[1].charinfo)
                    row.name = charinfo.firstname .. ' ' .. charinfo.lastname
                else
                    row.name = 'Unknown Driver'
                end
            end
        end
        cb(result)
    else
        cb({})
    end
end)

-- Job timeout system
CreateThread(function()
    while true do
        Wait(60000) -- Check every minute
        
        local currentTime = os.time()
        for src, activeJob in pairs(activeJobs) do
            if activeJob and activeJob.startTime then
                local timeElapsed = currentTime - activeJob.startTime
                local timeoutSeconds = Config.JobFailure.timeoutMinutes * 60
                
                if timeElapsed > timeoutSeconds then
                    -- Job timed out
                    TriggerEvent('zenith_delivery:failJob', 'time_expired')
                end
            end
        end
    end
end)
