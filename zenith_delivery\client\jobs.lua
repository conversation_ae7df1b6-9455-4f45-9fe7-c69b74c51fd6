-- Job-specific client logic for Zenith Delivery System

local QBCore = exports['qbx_core']:GetCoreObject()
local jobStartTime = 0
local distanceTraveled = 0
local lastPosition = nil

-- Job progress tracking
RegisterNetEvent('zenith_delivery:updateJobProgress', function(job, currentStop)
    currentJob = job
    
    -- Update UI with progress
    SendNUIMessage({
        action = 'updateJobProgress',
        data = {
            job = job,
            currentStop = currentStop,
            progress = (currentStop - 1) / #job.stops * 100
        }
    })
    
    -- Update blips for next stop
    UpdateJobBlips()
    
    QBCore.Functions.Notify('Proceed to the next delivery location.', 'inform')
end)

-- Distance tracking for active jobs
CreateThread(function()
    while true do
        Wait(5000) -- Update every 5 seconds
        
        if currentJob and currentVehicle then
            local ped = PlayerPedId()
            local vehicle = GetVehiclePedIsIn(ped, false)
            
            if vehicle == currentVehicle then
                local currentPos = GetEntityCoords(vehicle)
                
                if lastPosition then
                    local distance = #(currentPos - lastPosition)
                    distanceTraveled = distanceTraveled + distance
                end
                
                lastPosition = currentPos
            end
        else
            lastPosition = nil
        end
    end
end)

-- Job timer and status updates
CreateThread(function()
    while true do
        Wait(1000) -- Update every second
        
        if currentJob then
            local currentTime = GetGameTimer()
            local elapsedTime = (currentTime - jobStartTime) / 1000 -- Convert to seconds
            
            -- Send time update to UI
            SendNUIMessage({
                action = 'updateJobTimer',
                data = {
                    elapsedTime = elapsedTime,
                    estimatedTime = currentJob.estimatedTime * 60, -- Convert minutes to seconds
                    distanceTraveled = distanceTraveled
                }
            })
            
            -- Check for time warnings
            local estimatedSeconds = currentJob.estimatedTime * 60
            if currentJob.timePenalty and elapsedTime > estimatedSeconds * 1.2 then
                -- 20% over estimated time
                if math.floor(elapsedTime) % 30 == 0 then -- Every 30 seconds
                    QBCore.Functions.Notify('Warning: Delivery is taking longer than expected!', 'error')
                end
            end
        end
    end
end)

-- Special cargo handling
function HandleSpecialCargo(job, stop)
    if not job.specialRequirements then return true end
    
    local ped = PlayerPedId()
    local vehicle = GetVehiclePedIsIn(ped, false)
    
    -- Hazmat handling
    if job.cargoTypes and table.contains(job.cargoTypes, 'hazmat') then
        -- Check vehicle condition
        local engineHealth = GetVehicleEngineHealth(vehicle)
        if engineHealth < 800 then
            QBCore.Functions.Notify('Vehicle condition too poor for hazardous materials!', 'error')
            return false
        end
        
        -- Slower loading/unloading for hazmat
        local extraTime = 3000
        QBCore.Functions.Progressbar("hazmat_handling", "Carefully handling hazardous materials...", extraTime, false, true, {
            disableMovement = true,
            disableCarMovement = true,
            disableMouse = false,
            disableCombat = true,
        }, {}, {}, {}, function() -- Done
            -- Continue with normal handling
        end, function() -- Cancel
            QBCore.Functions.Notify("Hazmat handling cancelled.", 'error')
            return false
        end)
    end
    
    -- High value cargo security check
    if job.highValue then
        -- Random security event
        if math.random(1, 100) <= 10 then -- 10% chance
            TriggerSecurityEvent(job)
        end
    end
    
    return true
end

-- Security event for high value cargo
function TriggerSecurityEvent(job)
    QBCore.Functions.Notify('Security alert: Suspicious activity detected nearby!', 'error')
    
    -- Notify dispatch if enabled
    if Config.Dispatch.enabled and Config.Dispatch.policeAlert then
        local coords = GetEntityCoords(PlayerPedId())
        TriggerServerEvent('police:server:policeAlert', 'Cargo theft in progress', coords)
    end
    
    -- Create temporary wanted level or security response
    CreateThread(function()
        local alertTime = 60000 -- 1 minute alert
        local startTime = GetGameTimer()
        
        while GetGameTimer() - startTime < alertTime do
            Wait(1000)
            
            -- Check if player is still in area
            local playerCoords = GetEntityCoords(PlayerPedId())
            local alertCoords = GetEntityCoords(PlayerPedId()) -- Would be the alert location
            local distance = #(playerCoords - alertCoords)
            
            if distance > 500 then -- Player moved away
                QBCore.Functions.Notify('Security alert cleared.', 'success')
                break
            end
        end
    end)
end

-- Weather and time-based bonuses
function CheckEnvironmentalBonuses(job)
    local bonuses = {}
    
    -- Weather bonus
    local weather = GetCurrentWeather()
    if Config.SpecialConditions.weather[weather] then
        table.insert(bonuses, Config.SpecialConditions.weather[weather].message)
    end
    
    -- Time bonus
    local hour = GetClockHours()
    if (hour >= 22 or hour <= 6) and Config.SpecialConditions.timeOfDay.night then
        table.insert(bonuses, Config.SpecialConditions.timeOfDay.night.message)
    elseif (hour >= 7 and hour <= 9) or (hour >= 17 and hour <= 19) then
        table.insert(bonuses, Config.SpecialConditions.timeOfDay.rush_hour.message)
    end
    
    if #bonuses > 0 then
        SendNUIMessage({
            action = 'showBonuses',
            data = bonuses
        })
    end
end

-- Cargo loading/unloading with realistic timing
function HandleCargoAction(stop, stopIndex)
    local ped = PlayerPedId()
    local vehicle = GetVehiclePedIsIn(ped, false)
    
    if vehicle == 0 then
        QBCore.Functions.Notify('You must be in your delivery vehicle.', 'error')
        return false
    end
    
    -- Check special cargo requirements
    if not HandleSpecialCargo(currentJob, stop) then
        return false
    end
    
    local isPickup = stop.type == "pickup"
    local baseTime = isPickup and Config.Animations.loading.duration or Config.Animations.unloading.duration
    
    -- Adjust time based on cargo type and vehicle
    local timeMultiplier = 1.0
    if currentJob.cargoTypes then
        for _, cargoType in ipairs(currentJob.cargoTypes) do
            if cargoType == 'hazmat' or cargoType == 'chemicals' then
                timeMultiplier = 1.5
            elseif cargoType == 'luxury' or cargoType == 'electronics' then
                timeMultiplier = 1.2
            elseif cargoType == 'construction' or cargoType == 'oversized' then
                timeMultiplier = 1.3
            end
        end
    end
    
    local finalTime = math.floor(baseTime * timeMultiplier)
    
    -- Play appropriate animation
    local animDict = isPickup and Config.Animations.loading.dict or Config.Animations.unloading.dict
    local animName = isPickup and Config.Animations.loading.anim or Config.Animations.unloading.anim
    
    RequestAnimDict(animDict)
    while not HasAnimDictLoaded(animDict) do
        Wait(100)
    end
    
    TaskPlayAnim(ped, animDict, animName, 8.0, -8.0, finalTime, 1, 0, false, false, false)
    
    -- Show progress bar with dynamic text
    local actionText = isPickup and "Loading cargo..." or "Delivering cargo..."
    if timeMultiplier > 1.0 then
        actionText = actionText .. " (Special handling required)"
    end
    
    QBCore.Functions.Progressbar("cargo_action", actionText, finalTime, false, true, {
        disableMovement = true,
        disableCarMovement = true,
        disableMouse = false,
        disableCombat = true,
    }, {}, {}, {}, function() -- Done
        ClearPedTasks(ped)
        
        -- Complete the stop
        TriggerServerEvent('zenith_delivery:completeStop', stopIndex)
        
        -- Update local state
        currentJob.stops[stopIndex].completed = true
        
        -- Check environmental bonuses
        CheckEnvironmentalBonuses(currentJob)
        
        local successText = isPickup and "Cargo loaded successfully!" or "Cargo delivered successfully!"
        QBCore.Functions.Notify(successText, 'success')
        
        return true
    end, function() -- Cancel
        ClearPedTasks(ped)
        QBCore.Functions.Notify("Action cancelled.", 'error')
        return false
    end)
end

-- Utility function to check if table contains value
function table.contains(table, element)
    for _, value in pairs(table) do
        if value == element then
            return true
        end
    end
    return false
end

-- Job initialization
RegisterNetEvent('zenith_delivery:jobAccepted', function(job)
    jobStartTime = GetGameTimer()
    distanceTraveled = 0
    lastPosition = nil
    
    -- Store job reference
    currentJob = job
    
    -- Check environmental bonuses at start
    CheckEnvironmentalBonuses(job)
    
    -- Send job data to UI
    SendNUIMessage({
        action = 'jobStarted',
        data = {
            job = job,
            startTime = jobStartTime
        }
    })
end)

-- Job cleanup
function CleanupJobData()
    jobStartTime = 0
    distanceTraveled = 0
    lastPosition = nil
    currentJob = nil
    
    SendNUIMessage({
        action = 'jobEnded'
    })
end

-- Hook into job completion/failure
RegisterNetEvent('zenith_delivery:jobCompleted', function(completionData)
    completionData.distanceTraveled = distanceTraveled
    completionData.timeElapsed = (GetGameTimer() - jobStartTime) / 1000
    
    CleanupJobData()
end)

RegisterNetEvent('zenith_delivery:jobFailed', function(failureData)
    CleanupJobData()
end)
